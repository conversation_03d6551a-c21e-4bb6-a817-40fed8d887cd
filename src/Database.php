<?php

class Database {
    private PDO $pdo;
    
    public function __construct() {
        $dsn = Config::getDatabaseDsn();
        $username = Config::get('DB_USERNAME');
        $password = Config::get('DB_PASSWORD');
        
        $this->pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    }

    public function getPdo(): PDO {
        return $this->pdo;
    }
}
