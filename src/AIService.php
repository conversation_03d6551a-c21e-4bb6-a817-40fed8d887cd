<?php

class AIService {
    private string $apiKey;
    private Database $database;
    private string $assistantId;
    
    public function __construct(Database $database) {
        $this->apiKey = Config::get('OPENAI_API_KEY');
        $this->database = $database;
        $this->assistantId = Config::get('OPENAI_ASSISTANT_ID');
        if (!$this->apiKey) {
            throw new Exception('OpenAI API key not configured');
        }
        if (!$this->assistantId) {
            throw new Exception('OpenAI Assistant ID not configured');
        }
    }

    private function getOrCreateThreadId(string $phoneNumber): string {
        $stmt = $this->database->getPdo()->prepare("SELECT thread_id FROM conversation_threads WHERE phone_number = ?");
        $stmt->execute([$phoneNumber]);
        $row = $stmt->fetch();
        if ($row && !empty($row['thread_id'])) {
            return $row['thread_id'];
        }
        $threadId = $this->createThread();
        $stmt = $this->database->getPdo()->prepare("INSERT INTO conversation_threads (phone_number, thread_id) VALUES (?, ?)");
        $stmt->execute([$phoneNumber, $threadId]);
        return $threadId;
    }

    private function createThread(): string {
        $ch = curl_init('https://api.openai.com/v1/threads');
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ],
            CURLOPT_POSTFIELDS => '{}'
        ]);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($response === false || $httpCode !== 200) {
            throw new Exception("OpenAI create thread failed: HTTP {$httpCode}");
        }
        $data = json_decode($response, true);
        return $data['id'] ?? throw new Exception('No thread ID returned');
    }

    public function generateResponse(string $phoneNumber, string $userMessage): string {
        $threadId = $this->getOrCreateThreadId($phoneNumber);
        $ch = curl_init("https://api.openai.com/v1/threads/{$threadId}/messages");
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ],
            CURLOPT_POSTFIELDS => json_encode([
                'role' => 'user',
                'content' => $userMessage
            ])
        ]);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($response === false || $httpCode !== 201) {
            throw new Exception("OpenAI add message failed: HTTP {$httpCode}");
        }
        $ch = curl_init("https://api.openai.com/v1/threads/{$threadId}/runs");
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ],
            CURLOPT_POSTFIELDS => json_encode([
                'assistant_id' => $this->assistantId
            ])
        ]);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($response === false || $httpCode !== 201) {
            throw new Exception("OpenAI run assistant failed: HTTP {$httpCode}");
        }
        $data = json_decode($response, true);
        $runId = $data['id'] ?? throw new Exception('No run ID returned');
        $status = '';
        do {
            sleep(1);
            $ch = curl_init("https://api.openai.com/v1/threads/{$threadId}/runs/{$runId}");
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTPHEADER => [
                    'Authorization: Bearer ' . $this->apiKey
                ]
            ]);
            $response = curl_exec($ch);
            curl_close($ch);
            $data = json_decode($response, true);
            $status = $data['status'] ?? '';
        } while ($status && $status !== 'completed' && $status !== 'failed' && $status !== 'cancelled');
        if ($status !== 'completed') {
            throw new Exception('OpenAI assistant run did not complete successfully');
        }
        $ch = curl_init("https://api.openai.com/v1/threads/{$threadId}/messages?order=desc&limit=1");
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->apiKey
            ]
        ]);
        $response = curl_exec($ch);
        curl_close($ch);
        $data = json_decode($response, true);
        foreach ($data['data'] as $msg) {
            if ($msg['role'] === 'assistant') {
                return $msg['content'][0]['text']['value'] ?? 'Sorry, I could not generate a response.';
            }
        }
        return 'Sorry, I could not generate a response.';
    }
}
