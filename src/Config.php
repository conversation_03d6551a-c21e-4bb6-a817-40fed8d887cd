<?php

class Config {
    private static array $config = [];
    
    public static function load(string $envFile = '.env'): void {
        if (!file_exists($envFile)) {
            throw new Exception("Environment file not found: {$envFile}");
        }
        
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue; // Skip comments
            }
            
            list($key, $value) = explode('=', $line, 2);
            self::$config[trim($key)] = trim($value);
        }
    }
    
    public static function get(string $key, $default = null) {
        return self::$config[$key] ?? $default;
    }
    
    public static function getDatabaseDsn(): string {
        $host = self::get('DB_HOST');
        $port = self::get('DB_PORT');
        $dbname = self::get('DB_NAME');
        
        return "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
    }
}