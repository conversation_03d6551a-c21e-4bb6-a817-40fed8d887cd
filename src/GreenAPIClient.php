<?php

class GreenAPIClient {
    private const BASE_URL = 'https://api.green-api.com';
    private string $instanceId;
    private string $accessToken;
    private int $timeout;
    
    public function __construct() {
        $this->instanceId = Config::get('GREEN_API_INSTANCE_ID');
        $this->accessToken = Config::get('GREEN_API_ACCESS_TOKEN');
        $this->timeout = (int)Config::get('REQUEST_TIMEOUT', 30);
        
        if (!$this->instanceId || !$this->accessToken) {
            throw new Exception('Green API credentials not configured');
        }
    }
    
    public function sendMessage(string $chatId, string $message): array {
        $endpoint = "/waInstance{$this->instanceId}/sendMessage/{$this->accessToken}";
        
        $payload = [
            'chatId' => $chatId,
            'message' => $message
        ];
        
        return $this->makeRequest($endpoint, $payload);
    }
    
    private function makeRequest(string $endpoint, array $data): array {
        $url = self::BASE_URL . $endpoint;
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false || $httpCode !== 200) {
            throw new Exception("Green API request failed: HTTP {$httpCode}");
        }
        
        return json_decode($response, true) ?? [];
    }
}
