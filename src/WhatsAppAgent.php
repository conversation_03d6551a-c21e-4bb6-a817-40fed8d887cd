<?php

class WhatsAppAgent {
    private GreenAPIClient $greenApi;
    private AIService $aiService;
    private Database $database;
    
    public function __construct() {
        $this->database = new Database();
        $this->greenApi = new GreenAPIClient();
        $this->aiService = new AIService($this->database);
    }
    
    public function handleIncomingMessage(array $webhookData): void {
        try {
            $phoneNumber = $this->extractPhoneNumber($webhookData);
            $message = $this->extractMessage($webhookData);
            
            if (!$phoneNumber || !$message) {
                return;
            }
            
            $response = $this->aiService->generateResponse($phoneNumber, $message);
            $this->greenApi->sendMessage($phoneNumber, $response);
        } catch (Exception $e) {
            error_log("WhatsApp Agent Error: " . $e->getMessage());
        }
    }
    
    private function extractPhoneNumber(array $webhookData): ?string {
        return $webhookData['senderData']['chatId'] ?? null;
    }
    
    private function extractMessage(array $webhookData): ?string {
        return $webhookData['messageData']['textMessageData']['textMessage'] ?? null;
    }
}