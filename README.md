# WhatsApp AI Support Agent with OpenAI

## Overview

This system provides automated support for WhatsApp users by integrating Green API (for receiving WhatsApp messages) with the OpenAI Assistants API (for generating smart responses). The system maintains conversation context for each user by mapping their phone number to an OpenAI thread ID.

---

## How It Works — Step by Step

1. **A user sends a message on WhatsApp.**
2. **Green API** receives the message and sends a webhook (HTTP POST) to your server (e.g., to `webhook.php`).
3. **webhook.php** (or a similar file) calls `WhatsAppAgent` with the message data.
4. **WhatsAppAgent:**
    - Extracts the phone number and message content from the webhook.
    - Calls `AIService` to get a response.
    - Sends the response back to the user on WhatsApp via `GreenAPIClient`.
5. **AIService:**
    - Checks if a thread ID exists for the phone number in the database.
    - If not, creates a new thread in OpenAI and stores the mapping in the database.
    - Sends the message to the appropriate thread in OpenAI.
    - Runs the Assistant and waits for a response.
    - Returns the response to `WhatsAppAgent`.
6. **Database:**
    - Only used to store the mapping between phone numbers and thread IDs.

---

## Key Files and Components

### .env
Environment configuration file. Includes:
- Green API credentials
- OpenAI credentials (including Assistant ID)
- Database credentials

### Config.php
Loads values from `.env` and provides them to the rest of the code.

### Database.php
- Creates a connection to the MySQL database
- Provides access to the PDO instance for thread mapping operations

### AIService.php
- Manages all communication with the OpenAI Assistants API
- Creates/looks up a thread for each user by phone number
- Sends messages to the thread, runs the Assistant, and returns the response

### WhatsAppAgent.php
- Receives webhook data
- Extracts phone number and message
- Calls `AIService` for a response
- Sends the response back to the user on WhatsApp

### GreenAPIClient.php
- Sends messages back to WhatsApp users via Green API

### conversation_threads Table in the Database
```sql
CREATE TABLE conversation_threads (
    phone_number VARCHAR(20) PRIMARY KEY,
    thread_id VARCHAR(100) NOT NULL
);
```
- Maps each phone number to an OpenAI thread ID

---

## Flow Diagram
1. User sends message → Green API sends webhook → WhatsAppAgent extracts data → AIService checks/creates thread → Sends message to OpenAI → Gets response → WhatsAppAgent sends to user.

---

## Important Notes
- No messages or knowledge are stored in the database, only thread mapping.
- All context and knowledge for the assistant are managed in the OpenAI Assistant (including files uploaded on the OpenAI platform).
- You can test locally by calling `WhatsAppAgent` directly with mock data.

---

## How to Extend or Modify
- To change the assistant: update the `ASSISTANT_ID` in `.env`.
- To add more logic: extend `WhatsAppAgent` or `AIService`.
- To add another channel: create another agent similar to `WhatsAppAgent`.

---

## Summary
This system provides a simple and efficient bridge between WhatsApp and OpenAI, with minimal local logic. All context and intelligence are managed by OpenAI. 