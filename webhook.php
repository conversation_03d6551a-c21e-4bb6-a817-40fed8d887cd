<?php

require_once 'src/Config.php';
require_once 'src/Database.php';
require_once 'src/GreenAPIClient.php';
require_once 'src/AIService.php';
require_once 'src/WhatsAppAgent.php';

// Load configuration
Config::load();

// Initialize agent
$agent = new WhatsAppAgent();

// Handle incoming webhook
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = file_get_contents('php://input');
    $webhookData = json_decode($input, true);
    
    // Log incoming webhook for debugging
    error_log("Webhook received: " . $input);
    
    if ($webhookData) {
        $agent->handleIncomingMessage($webhookData);
    }
    
    http_response_code(200);
    echo "OK";
} else {
    http_response_code(405);
    echo "Method not allowed";
}