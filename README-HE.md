# סוכן תמיכה אוטומטי ל-WhatsApp עם OpenAI

## סקירה כללית

מערכת זו מאפשרת מתן מענה אוטומטי ללקוחות ב-WhatsApp באמצעות אינטגרציה בין Green API (לקבלת הודעות WhatsApp) לבין OpenAI Assistants API (ליצירת תגובות חכמות). המערכת שומרת הקשר שיחה לכל משתמש באמצעות מיפוי בין מספר הטלפון שלו ל-thread ID של OpenAI.

---

## איך זה עובד - שלב אחר שלב

1. **לקוח שולח הודעה ב-WhatsApp**
2. **Green API** מקבל את ההודעה ושולח קריאת webhook (POST) לשרת שלך (למשל ל-webhook.php).
3. **webhook.php** (או קובץ דומה) קורא ל-WhatsAppAgent עם נתוני ההודעה.
4. **WhatsAppAgent**:
    - שולף את מספר הטלפון ואת תוכן ההודעה מה-webhook.
    - פונה ל-AIService לקבלת תגובה.
    - שולח את התגובה חזרה ללקוח ב-WhatsApp דרך GreenAPIClient.
5. **AIService**:
    - בודק אם קיים thread ID למספר הטלפון במסד הנתונים.
    - אם לא קיים, יוצר thread חדש ב-OpenAI ושומר את המיפוי במסד הנתונים.
    - שולח את ההודעה ל-thread המתאים ב-OpenAI.
    - מריץ את הסוכן (Assistant) וממתין לתגובה.
    - מחזיר את התגובה ל-WhatsAppAgent.
6. **Database**:
    - משמש רק לשמירת מיפוי בין מספרי טלפון ל-thread IDs.

---

## קבצים ורכיבים עיקריים

### .env
קובץ הגדרות סביבה. כולל:
- פרטי Green API
- מפתחות OpenAI (כולל Assistant ID)
- פרטי מסד נתונים

### Config.php
טוען ערכים מה-.env ומספק אותם לשאר הקוד.

### Database.php
- יוצר חיבור למסד הנתונים (MySQL)
- מספק גישה ל-PDO עבור פעולות thread mapping

### AIService.php
- מנהל את כל התקשורת עם OpenAI Assistants API
- יוצר/שולף thread לכל משתמש לפי מספר טלפון
- שולח הודעות ל-thread, מריץ את הסוכן, ומחזיר תגובה

### WhatsAppAgent.php
- מקבל נתוני webhook
- שולף מספר טלפון והודעה
- פונה ל-AIService לקבלת תגובה
- שולח את התגובה חזרה ללקוח ב-WhatsApp

### GreenAPIClient.php
- שולח הודעות חזרה ללקוחות ב-WhatsApp דרך Green API

### טבלת conversation_threads במסד הנתונים
```sql
CREATE TABLE conversation_threads (
    phone_number VARCHAR(20) PRIMARY KEY,
    thread_id VARCHAR(100) NOT NULL
);
```
- ממפה כל מספר טלפון ל-thread ID של OpenAI

---

## תרשים זרימה (Flow)
1. לקוח שולח הודעה → Green API שולח webhook → WhatsAppAgent שולף נתונים → AIService בודק/יוצר thread → שולח הודעה ל-OpenAI → מקבל תגובה → WhatsAppAgent שולח ללקוח.

---

## הערות חשובות
- אין שמירת הודעות או ידע במסד הנתונים, רק מיפוי thread.
- כל ההקשר והידע של הסוכן מנוהלים ב-OpenAI Assistant (כולל קבצי ידע שמועלים בפלטפורמה של OpenAI).
- ניתן לבדוק מקומית ע"י קריאה ישירה ל-WhatsAppAgent עם נתוני דמה.

---

## איך להרחיב או לשנות
- להחליף את הסוכן: עדכנו את ה-ASSISTANT_ID ב-.env.
- להוסיף לוגיקה נוספת: הוסיפו ב-WhatsAppAgent או AIService.
- להוסיף ערוץ נוסף: צרו Agent נוסף בדומה ל-WhatsAppAgent.

---

## סיכום
מערכת זו מספקת גשר פשוט ויעיל בין WhatsApp ל-OpenAI, עם מינימום קוד לוגי מקומי. כל ההקשר והאינטליגנציה מנוהלים ע"י OpenAI. 